- **Property:** break-after
- **Shorthand:** brka  
  Utilities for controlling how a column or page should break after an element.

```css
brkaAuto {
  break-after: auto;
}
brkaAvoid {
  break-after: avoid;
}
brkaAlways {
  break-after: always;
}
brkaAll {
  break-after: all;
}
brkaPage {
  break-after: page;
}
brkaLeft {
  break-after: left;
}
brkaRight {
  break-after: right;
}
brkaRecto {
  break-after: recto;
}
brkaVerso {
  break-after: verso;
}
brkaColumn {
  break-after: column;
}
brkaAvoidPage {
  break-after: avoid-page;
}
brkaAvoidColumn {
  break-after: avoid-column;
}
brkaAvoidRegion {
  break-after: avoid-region;
}
```

The `break-after` property controls how page, column, or region breaks should behave after an element. This is particularly useful for print layouts, paginated content, and multi-column layouts.

## Usage Examples

### Default Behavior with brkaAuto

```html
<p class="brkaAuto">
  This paragraph uses the default break behavior. The browser will determine if
  a break should occur after this element.
</p>
```

With `brkaAuto`, the browser automatically determines whether to insert a break after the element based on available space and other layout considerations.

![brkaAuto example](./img/brka_brkaAuto.png)

_The browser decides whether to insert a page break after this element._

### Preventing Breaks with brkaAvoid

```html
<h2>Section Title</h2>
<p class="brkaAvoid">
  This paragraph has brkaAvoid applied. The browser will try to avoid breaking
  after this paragraph, keeping it together with the content that follows.
</p>
<p>This paragraph should ideally stay with the previous paragraph.</p>
```

The `brkaAvoid` class tells the browser to avoid inserting a page or column break after the element if possible.

![brkaAvoid example](./img/brka_brkaAvoid.png)

_Notice how the content stays together despite being near a natural page break point._

### Forcing Page Breaks with brkaAlways

```html
<h2>Chapter 1</h2>
<p>This is the end of Chapter 1.</p>
<p class="brkaAlways">
  This paragraph forces a page break after it. The next content will start on a
  new page.
</p>
<h2>Chapter 2</h2>
<p>This content appears on a new page.</p>
```

The `brkaAlways` class forces a page break after the element, ensuring the following content starts on a new page.

![brkaAlways example](./img/brka_brkaAlways.png)

_A page break is forced after the paragraph with brkaAlways applied._

### Page Break with brkaPage

```html
<p>This is the end of the current page.</p>
<p class="brkaPage">
  This paragraph forces a page break after it, similar to brkaAlways but
  specifically for paged media.
</p>
<p>This content appears on a new page.</p>
```

The `brkaPage` class is similar to `brkaAlways` but is specifically designed for paged media contexts.

![brkaPage example](./img/brka_brkaPage.png)

_A page break is forced after the element with brkaPage applied._

### Column Breaks with brkaColumn

```html
<div style="column-count: 2; column-gap: 40px;">
  <p>This is the first part of content in a multi-column layout.</p>
  <p class="brkaColumn">
    This paragraph forces a column break. The next content will start in a new
    column.
  </p>
  <p>
    This content appears at the top of the next column due to the column break.
  </p>
</div>
```

In multi-column layouts, `brkaColumn` forces the content after the element to begin in a new column.

![brkaColumn example](./img/brka_brkaColumn.png)

_The content after the element with brkaColumn applied starts in the next column._

### Page Position Control with brkaLeft and brkaRight

```html
<p class="brkaLeft">
  This paragraph forces a page break, and the next content will appear on a left
  page in a spread.
</p>
```

```html
<p class="brkaRight">
  This paragraph forces a page break, and the next content will appear on a
  right page in a spread.
</p>
```

The `brkaLeft` and `brkaRight` classes are useful for controlling content positioning in book-like layouts with facing pages.

![brkaLeft and brkaRight example](./img/brka_brkaLeftRight.png)

_Diagram showing how content flows to specific pages in a spread._

### Recto and Verso Page Control

```html
<p class="brkaRecto">
  This paragraph forces a page break, and the next content will appear on a
  recto (right-hand) page.
</p>
```

```html
<p class="brkaVerso">
  This paragraph forces a page break, and the next content will appear on a
  verso (left-hand) page.
</p>
```

The `brkaRecto` and `brkaVerso` classes provide alternative ways to control page positioning in book layouts.

![brkaRecto and brkaVerso example](./img/brka_brkaRectoVerso.png)

_Diagram showing content flow to recto (right) and verso (left) pages in a book layout._

### Specific Avoidance with brkaAvoidPage, brkaAvoidColumn, and brkaAvoidRegion

```html
<p class="brkaAvoidPage">
  This paragraph prevents a page break after it, but may allow column breaks.
</p>
```

```html
<div style="column-count: 3; column-gap: 20px;">
  <p>First column content.</p>
  <p class="brkaAvoidColumn">
    This paragraph prevents a column break after it, but may allow page breaks.
  </p>
  <p>This content should stay in the same column as the previous paragraph.</p>
</div>
```

These specialized avoidance classes provide more granular control over different types of breaks.

![brkaAvoidPage and brkaAvoidColumn example](./img/brka_brkaAvoidSpecific.png)

_The image shows how specific break avoidance affects layout in different contexts._

## Browser Support and Considerations

- The `break-after` property is primarily designed for paged media (like print) and may have limited effect in standard web viewing contexts
- For consistent results across browsers, combine with appropriate CSS for print media queries
- In multi-column layouts, these properties work in most modern browsers
- For older browsers, consider fallback strategies using alternative pagination methods

## Related Properties

- [break-before](./break-before.md) - Controls breaks before an element
- [break-inside](./break-inside.md) - Controls breaks within an element
